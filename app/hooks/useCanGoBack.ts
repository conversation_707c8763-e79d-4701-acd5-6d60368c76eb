import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigationType } from 'react-router';

export default function useCanGoBack() {
  const location = useLocation();
  const navigationType = useNavigationType();
  const prevPath = useRef<string | null>(null);
  const [canGoBack, setCanGoBack] = useState(false);

  const getRouteGroup = (path: string | null) => {
    if (path?.startsWith('/voucher')) return 'voucher';
    // if (path?.startsWith('/banner')) return 'banner';
    return 'other';
  };

  useEffect(() => {
    const currentGroup = getRouteGroup(location.pathname);
    const prevGroup = getRouteGroup(prevPath.current);

    setCanGoBack(prevGroup === currentGroup && currentGroup !== 'other' && navigationType === 'PUSH');

    prevPath.current = location.pathname;
  }, [location]);

  return {
    canGoBack,
  };
}
