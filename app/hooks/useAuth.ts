import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import useAuthStore from '../store/authStore';
import { loginApi, logoutApi } from '@/api/api';

export const useAuth = () => {
  const queryClient = useQueryClient();
  const setUser = useAuthStore((state) => state.setUser);
  const user = useAuthStore((state) => state.user);

  const { isLoading, isError, error } = useQuery<User | null, Error>({
    queryKey: ['currentUser'],
    queryFn: async () => {
      try {
        // const response = await meApi();

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const response: any = {
          data: {
            user: {
              id: '123',
              username: 'user',
              name: 'Suntel Super Admin',
              role: 'admin',
              email: '<EMAIL>',
              image: 'https://github.com/shadcn.png'
            }
          }
        };
        // const response: any = {
        //   data: null,
        // };
        setUser(response.data.user);
        return response.data;
      } catch (err: AxiosError) {
        if (err.response && err.response.status === 401) {
          setUser(null);
          return null;
        }
        throw err;
      }
    }
  });

  const loginMutation = useMutation({
    mutationKey: ['login'],
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      return await loginApi(email, password);
    },
    onSuccess: (data) => {
      setUser(data.data.user);
      queryClient.invalidateQueries({
        queryKey: ['currentUser']
      });
    },
    onError: (err) => {
      console.error('Login mutation error:', err);
    }
  });

  const logoutMutation = useMutation({
    mutationKey: ['logout'],
    mutationFn: async () => {
      return await logoutApi();
    },
    onSuccess: () => {
      setUser(null);
      queryClient.invalidateQueries({
        queryKey: ['currentUser']
      });
      queryClient.removeQueries({
        queryKey: ['currentUser']
      });
    },
    onError: (err) => {
      console.error('Logout mutation error:', err);

      setUser(null);
    }
  });

  return {
    user,
    isLoadingAuth: isLoading,
    authError: isError ? error.message : null,
    isLoggedIn: !!user,
    login: loginMutation.mutateAsync,
    logout: logoutMutation.mutateAsync,
    loginLoading: loginMutation.isPending,
    loginError: loginMutation.isError ? loginMutation.error?.message : null
  };
};
