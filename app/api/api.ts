// lib/api.ts
import axios, { type AxiosResponse } from 'axios';

// Get your external API base URL from environment variables
// IMPORTANT: Use NEXT_PUBLIC_ prefix for client-side accessible variables
const VITE_BASE_URL = import.meta.env.VITE_BASE_URL;

console.log('VITE_BASE_URL:', VITE_BASE_URL); // Debugging line to check the base URL

if (!VITE_BASE_URL) {
  throw new Error('VITE_BASE_URL is not defined in environment variables.');
}

const api = axios.create({
  baseURL: VITE_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    // You might add authorization headers here if using a simple token system
    // 'Authorization': `Bearer ${token}`
  },
  withCredentials: true, // If your API uses cookies/sessions
});

// Optional: Add request/response interceptors for common tasks
api.interceptors.request.use(
  (config) => {
    // Example: Add a token from localStorage or a global state
    // const token = localStorage.getItem('accessToken');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Example: Handle 401 Unauthorized errors
    // if (error.response && error.response.status === 401) {
    //   // Redirect to login or refresh token
    //   window.location.href = '/login';
    // }
    return Promise.reject(error);
  },
);
// You can create specific response interfaces for different endpoints
export const loginApi = (email: string, password: string): Promise<AxiosResponse<LoginResponse>> =>
  api.post<LoginResponse>('/login', { email, password });

export const meApi = (): Promise<AxiosResponse<User>> => api.get<User>('/me');

export const logoutApi = (): Promise<AxiosResponse<{ message: string }>> => api.post<{ message: string }>('/logout');

export const publicApi = (): Promise<AxiosResponse<{ message: string }>> => api.get<{ message: string }>('/public');

export default api;
