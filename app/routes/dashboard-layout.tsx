import { AppSidebar } from '@/components/ui/common/app-sidebar';
import DashboardHeader from '@/components/ui/common/dashboard-header';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Outlet } from 'react-router';

export default function DashboardLayout() {
  return (
    <div className="min-h-screen flex ">
      <SidebarProvider>
        <AppSidebar />
        <main className="flex flex-1 flex-col bg-background">
          <div className="sticky top-0 z-10 bg-background">
            <DashboardHeader />
          </div>
          <div className="flex-1 min-h-0 overflow-auto">
            <Outlet />
          </div>
        </main>
      </SidebarProvider>
    </div>
  );
}
