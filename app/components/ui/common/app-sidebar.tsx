import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '../sidebar';
import beasyIcon from '@/asset/images/beasy-icon.png';
import IconAssets from '@/asset/icons/icon-assets';
import { NavLink, useLocation } from 'react-router';

// Menu items.
const items = [
  {
    title: 'Home',
    url: '/',
    icon: IconAssets.Dashboard,
  },
  {
    title: 'Order',
    url: '/order',
    icon: IconAssets.Order,
  },
  {
    title: 'User',
    url: '/user',
    icon: IconAssets.User,
  },
  {
    title: 'Customer',
    url: '/customer',
    icon: IconAssets.Customer,
  },
  {
    title: 'Voucher',
    url: '/voucher',
    icon: IconAssets.Voucher,
  },
  {
    title: 'Banner',
    url: '/banner',
    icon: IconAssets.Banner,
  },
  {
    title: 'Service Bundle',
    url: '/service-bundle',
    icon: IconAssets.ServiceBundle,
  },
  {
    title: 'Audit Log',
    url: '/audit-log',
    icon: IconAssets.AuditLog,
  },
  {
    title: 'Top Up',
    url: '/top-up',
    icon: IconAssets.TopUp,
  },
  {
    title: 'Referral Program',
    url: '/referral-program',
    icon: IconAssets.ReferralProgram,
  },
  {
    title: 'Notifications',
    url: '/notifications',
    icon: IconAssets.Notifications,
  },
];

export function AppSidebar() {
  const location = useLocation();

  return (
    <Sidebar variant="inset" className="p-0">
      <SidebarContent className="p-6 bg-background">
        <SidebarGroup className="gap-6">
          <div className="flex flex-row items-center gap-3 h-[72px]">
            <img src={beasyIcon} className="w-12 h-12" />
            <SidebarGroupLabel>bEasy</SidebarGroupLabel>
          </div>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild size="lg" isActive={location.pathname === item.url}>
                    <NavLink to={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
