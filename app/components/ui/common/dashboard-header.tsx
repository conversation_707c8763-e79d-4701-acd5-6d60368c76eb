import { getPageTitle } from '@/constants/constants';
import { useLocation, useNavigate } from 'react-router';
import ProfileAvatar from './profile-avatar';
import useCanGoBack from '@/hooks/useCanGoBack';
import { Bell, ChevronLeft } from 'lucide-react';
import { Button } from '../button';

export default function DashboardHeader() {
  const location = useLocation();
  const { canGoBack } = useCanGoBack();
  const navigate = useNavigate();
  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="h-[88px] p-6 pl-0 flex w-full items-center flex-row">
      <div className="flex flex-1 flex-row gap-4 items-center">
        {canGoBack && (
          <Button variant="ghost" size="icon" onClick={handleBack} disabled={!canGoBack}>
            <ChevronLeft />
          </Button>
        )}

        <p className="text-xl font-bold">{getPageTitle(location.pathname)}</p>
      </div>
      <Button variant="ghost" size="icon" className="mr-4">
        <Bell className="!w-6 !h-6" />
      </Button>
      <ProfileAvatar />
    </div>
  );
}
