import useAuthStore from '@/store/authStore';
import { Avatar, AvatarFallback, AvatarImage } from '../avatar';

export default function ProfileAvatar() {
  const { user } = useAuthStore();

  return (
    <div className="flex flex-row gap-3 justify-center items-center">
      <Avatar className="rounded-sm size-10">
        <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
        <AvatarFallback>CN</AvatarFallback>
      </Avatar>
      <div>
        <p className="font-semibold">{user?.name}</p>
        <p>{user?.role}</p>
      </div>
    </div>
  );
}
